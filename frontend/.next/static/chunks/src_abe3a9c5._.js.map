{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/utils/theme.ts"], "sourcesContent": ["// Theme utility functions\nexport const getSystemTheme = (): 'light' | 'dark' => {\n  if (typeof window === 'undefined') return 'light';\n  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n};\n\nexport const getStoredTheme = (): 'light' | 'dark' | null => {\n  if (typeof window === 'undefined') return null;\n  return localStorage.getItem('theme') as 'light' | 'dark' | null;\n};\n\nexport const setStoredTheme = (theme: 'light' | 'dark'): void => {\n  if (typeof window === 'undefined') return;\n  localStorage.setItem('theme', theme);\n};\n\nexport const applyTheme = (theme: 'light' | 'dark'): void => {\n  if (typeof window === 'undefined') return;\n  \n  const root = window.document.documentElement;\n  \n  if (theme === 'dark') {\n    root.classList.add('dark');\n  } else {\n    root.classList.remove('dark');\n  }\n};\n\nexport const initializeTheme = (): 'light' | 'dark' => {\n  const storedTheme = getStoredTheme();\n  const theme = storedTheme || getSystemTheme();\n  \n  applyTheme(theme);\n  setStoredTheme(theme);\n  \n  return theme;\n};\n"], "names": [], "mappings": "AAAA,0BAA0B;;;;;;;;AACnB,MAAM,iBAAiB;IAC5B,uCAAmC;;IAAc;IACjD,OAAO,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;AAC9E;AAEO,MAAM,iBAAiB;IAC5B,uCAAmC;;IAAW;IAC9C,OAAO,aAAa,OAAO,CAAC;AAC9B;AAEO,MAAM,iBAAiB,CAAC;IAC7B,uCAAmC;;IAAM;IACzC,aAAa,OAAO,CAAC,SAAS;AAChC;AAEO,MAAM,aAAa,CAAC;IACzB,uCAAmC;;IAAM;IAEzC,MAAM,OAAO,OAAO,QAAQ,CAAC,eAAe;IAE5C,IAAI,UAAU,QAAQ;QACpB,KAAK,SAAS,CAAC,GAAG,CAAC;IACrB,OAAO;QACL,KAAK,SAAS,CAAC,MAAM,CAAC;IACxB;AACF;AAEO,MAAM,kBAAkB;IAC7B,MAAM,cAAc;IACpB,MAAM,QAAQ,eAAe;IAE7B,WAAW;IACX,eAAe;IAEf,OAAO;AACT", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/hooks/useTheme.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { initializeTheme, applyTheme, setStoredTheme } from '@/utils/theme';\n\nexport const useTheme = () => {\n  const [theme, setTheme] = useState<'light' | 'dark'>('light');\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    const initialTheme = initializeTheme();\n    setTheme(initialTheme);\n    setIsLoading(false);\n  }, []);\n\n  const toggleTheme = () => {\n    const newTheme = theme === 'light' ? 'dark' : 'light';\n    setTheme(newTheme);\n    applyTheme(newTheme);\n    setStoredTheme(newTheme);\n  };\n\n  return {\n    theme,\n    toggleTheme,\n    isLoading,\n  };\n};\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAKO,MAAM,WAAW;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,eAAe,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD;YACnC,SAAS;YACT,aAAa;QACf;6BAAG,EAAE;IAEL,MAAM,cAAc;QAClB,MAAM,WAAW,UAAU,UAAU,SAAS;QAC9C,SAAS;QACT,CAAA,GAAA,wHAAA,CAAA,aAAU,AAAD,EAAE;QACX,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE;IACjB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;GAtBa", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/services/httpbase.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport Cookies from 'js-cookie';\nimport { ApiError } from '@/types';\n\nclass HttpBaseService {\n  private api: AxiosInstance;\n  private baseURL: string;\n\n  constructor() {\n    this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';\n    \n    this.api = axios.create({\n      baseURL: this.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // Request interceptor to add auth token\n    this.api.interceptors.request.use(\n      (config) => {\n        const token = this.getToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor for error handling\n    this.api.interceptors.response.use(\n      (response: AxiosResponse) => {\n        return response;\n      },\n      (error) => {\n        const apiError: ApiError = {\n          message: error.response?.data?.message || error.message || 'An error occurred',\n          status: error.response?.status || 500,\n          details: error.response?.data?.details || null,\n        };\n\n        // Handle 401 errors (unauthorized)\n        if (error.response?.status === 401) {\n          this.removeToken();\n          // Redirect to login if not already there\n          if (typeof window !== 'undefined' && !window.location.pathname.includes('/auth')) {\n            window.location.href = '/auth/login';\n          }\n        }\n\n        return Promise.reject(apiError);\n      }\n    );\n  }\n\n  // Token management\n  private getToken(): string | null {\n    if (typeof window === 'undefined') return null;\n    return Cookies.get('access_token') || localStorage.getItem('access_token');\n  }\n\n  public setToken(token: string): void {\n    if (typeof window === 'undefined') return;\n    Cookies.set('access_token', token, { expires: 7, secure: true, sameSite: 'strict' });\n    localStorage.setItem('access_token', token);\n  }\n\n  public removeToken(): void {\n    if (typeof window === 'undefined') return;\n    Cookies.remove('access_token');\n    localStorage.removeItem('access_token');\n  }\n\n  // HTTP Methods\n  public async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.api.get<T>(url, config);\n    return response.data;\n  }\n\n  public async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.api.post<T>(url, data, config);\n    return response.data;\n  }\n\n  public async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.api.put<T>(url, data, config);\n    return response.data;\n  }\n\n  public async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.api.patch<T>(url, data, config);\n    return response.data;\n  }\n\n  public async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.api.delete<T>(url, config);\n    return response.data;\n  }\n\n  // Form data methods for file uploads\n  public async postFormData<T = any>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.api.post<T>(url, formData, {\n      ...config,\n      headers: {\n        ...config?.headers,\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  }\n\n  // OAuth2 login method (for form-encoded data)\n  public async postFormEncoded<T = any>(url: string, data: Record<string, string>, config?: AxiosRequestConfig): Promise<T> {\n    const formData = new URLSearchParams();\n    Object.entries(data).forEach(([key, value]) => {\n      formData.append(key, value);\n    });\n\n    const response = await this.api.post<T>(url, formData, {\n      ...config,\n      headers: {\n        ...config?.headers,\n        'Content-Type': 'application/x-www-form-urlencoded',\n      },\n    });\n    return response.data;\n  }\n\n  // Health check\n  public async healthCheck(): Promise<any> {\n    return this.get('/health');\n  }\n\n  // Get base URL\n  public getBaseURL(): string {\n    return this.baseURL;\n  }\n}\n\n// Create and export singleton instance\nconst httpbase = new HttpBaseService();\nexport default httpbase;\n"], "names": [], "mappings": ";;;AASmB;AATnB;AACA;;;AAGA,MAAM;IACI,IAAmB;IACnB,QAAgB;IAExB,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;QAElD,IAAI,CAAC,GAAG,GAAG,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YACtB,SAAS,IAAI,CAAC,OAAO;YACrB,SAAS;YACT,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,iBAAiB;IACxB;IAEQ,oBAAoB;QAC1B,wCAAwC;QACxC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAC/B,CAAC;YACC,MAAM,QAAQ,IAAI,CAAC,QAAQ;YAC3B,IAAI,OAAO;gBACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;YAClD;YACA,OAAO;QACT,GACA,CAAC;YACC,OAAO,QAAQ,MAAM,CAAC;QACxB;QAGF,0CAA0C;QAC1C,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAChC,CAAC;YACC,OAAO;QACT,GACA,CAAC;YACC,MAAM,WAAqB;gBACzB,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;gBAC3D,QAAQ,MAAM,QAAQ,EAAE,UAAU;gBAClC,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;YAC5C;YAEA,mCAAmC;YACnC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,IAAI,CAAC,WAAW;gBAChB,yCAAyC;gBACzC,IAAI,aAAkB,eAAe,CAAC,OAAO,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU;oBAChF,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACzB;YACF;YAEA,OAAO,QAAQ,MAAM,CAAC;QACxB;IAEJ;IAEA,mBAAmB;IACX,WAA0B;QAChC,uCAAmC;;QAAW;QAC9C,OAAO,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,aAAa,OAAO,CAAC;IAC7D;IAEO,SAAS,KAAa,EAAQ;QACnC,uCAAmC;;QAAM;QACzC,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,gBAAgB,OAAO;YAAE,SAAS;YAAG,QAAQ;YAAM,UAAU;QAAS;QAClF,aAAa,OAAO,CAAC,gBAAgB;IACvC;IAEO,cAAoB;QACzB,uCAAmC;;QAAM;QACzC,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,aAAa,UAAU,CAAC;IAC1B;IAEA,eAAe;IACf,MAAa,IAAa,GAAW,EAAE,MAA2B,EAAc;QAC9E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAI,KAAK;QAC5C,OAAO,SAAS,IAAI;IACtB;IAEA,MAAa,KAAc,GAAW,EAAE,IAAU,EAAE,MAA2B,EAAc;QAC3F,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAI,KAAK,MAAM;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAa,IAAa,GAAW,EAAE,IAAU,EAAE,MAA2B,EAAc;QAC1F,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAI,KAAK,MAAM;QAClD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAa,MAAe,GAAW,EAAE,IAAU,EAAE,MAA2B,EAAc;QAC5F,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAI,KAAK,MAAM;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAa,OAAgB,GAAW,EAAE,MAA2B,EAAc;QACjF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAI,KAAK;QAC/C,OAAO,SAAS,IAAI;IACtB;IAEA,qCAAqC;IACrC,MAAa,aAAsB,GAAW,EAAE,QAAkB,EAAE,MAA2B,EAAc;QAC3G,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAI,KAAK,UAAU;YACrD,GAAG,MAAM;YACT,SAAS;gBACP,GAAG,QAAQ,OAAO;gBAClB,gBAAgB;YAClB;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,8CAA8C;IAC9C,MAAa,gBAAyB,GAAW,EAAE,IAA4B,EAAE,MAA2B,EAAc;QACxH,MAAM,WAAW,IAAI;QACrB,OAAO,OAAO,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACxC,SAAS,MAAM,CAAC,KAAK;QACvB;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAI,KAAK,UAAU;YACrD,GAAG,MAAM;YACT,SAAS;gBACP,GAAG,QAAQ,OAAO;gBAClB,gBAAgB;YAClB;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,MAAa,cAA4B;QACvC,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB;IAEA,eAAe;IACR,aAAqB;QAC1B,OAAO,IAAI,CAAC,OAAO;IACrB;AACF;AAEA,uCAAuC;AACvC,MAAM,WAAW,IAAI;uCACN", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/services/authService.ts"], "sourcesContent": ["import httpbase from './httpbase';\nimport { LoginRequest, RegisterRequest, LoginResponse, User } from '@/types';\n\nclass AuthService {\n  // Login user\n  async login(credentials: LoginRequest): Promise<LoginResponse> {\n    try {\n      const response = await httpbase.postFormEncoded<LoginResponse>('/api/auth/login', {\n        username: credentials.username,\n        password: credentials.password,\n        grant_type: 'password',\n      });\n\n      if (response.access_token) {\n        httpbase.setToken(response.access_token);\n      }\n\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  // Register user\n  async register(data: RegisterRequest): Promise<LoginResponse> {\n    try {\n      const response = await httpbase.post<LoginResponse>('/api/auth/register', data);\n\n      if (response.access_token) {\n        httpbase.setToken(response.access_token);\n      }\n\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  // Get current user info\n  async getCurrentUser(): Promise<User> {\n    try {\n      return await httpbase.get<User>('/api/auth/me');\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  // Update current user\n  async updateCurrentUser(data: Partial<User>): Promise<User> {\n    try {\n      return await httpbase.put<User>('/api/auth/me', data);\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  // Check if user is authenticated\n  async checkAuth(): Promise<boolean> {\n    try {\n      await httpbase.get('/api/auth/check');\n      return true;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  // Logout user\n  async logout(): Promise<void> {\n    try {\n      await httpbase.post('/api/auth/logout');\n    } catch (error) {\n      // Continue with logout even if API call fails\n      console.warn('Logout API call failed:', error);\n    } finally {\n      httpbase.removeToken();\n    }\n  }\n\n  // Check if user has valid token\n  hasValidToken(): boolean {\n    if (typeof window === 'undefined') return false;\n    const token = httpbase['getToken']();\n    return !!token;\n  }\n}\n\n// Create and export singleton instance\nconst authService = new AuthService();\nexport default authService;\n"], "names": [], "mappings": ";;;AAAA;;AAGA,MAAM;IACJ,aAAa;IACb,MAAM,MAAM,WAAyB,EAA0B;QAC7D,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAQ,CAAC,eAAe,CAAgB,mBAAmB;gBAChF,UAAU,YAAY,QAAQ;gBAC9B,UAAU,YAAY,QAAQ;gBAC9B,YAAY;YACd;YAEA,IAAI,SAAS,YAAY,EAAE;gBACzB,8HAAA,CAAA,UAAQ,CAAC,QAAQ,CAAC,SAAS,YAAY;YACzC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,gBAAgB;IAChB,MAAM,SAAS,IAAqB,EAA0B;QAC5D,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAQ,CAAC,IAAI,CAAgB,sBAAsB;YAE1E,IAAI,SAAS,YAAY,EAAE;gBACzB,8HAAA,CAAA,UAAQ,CAAC,QAAQ,CAAC,SAAS,YAAY;YACzC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,wBAAwB;IACxB,MAAM,iBAAgC;QACpC,IAAI;YACF,OAAO,MAAM,8HAAA,CAAA,UAAQ,CAAC,GAAG,CAAO;QAClC,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,MAAM,kBAAkB,IAAmB,EAAiB;QAC1D,IAAI;YACF,OAAO,MAAM,8HAAA,CAAA,UAAQ,CAAC,GAAG,CAAO,gBAAgB;QAClD,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,iCAAiC;IACjC,MAAM,YAA8B;QAClC,IAAI;YACF,MAAM,8HAAA,CAAA,UAAQ,CAAC,GAAG,CAAC;YACnB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,cAAc;IACd,MAAM,SAAwB;QAC5B,IAAI;YACF,MAAM,8HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC;QACtB,EAAE,OAAO,OAAO;YACd,8CAA8C;YAC9C,QAAQ,IAAI,CAAC,2BAA2B;QAC1C,SAAU;YACR,8HAAA,CAAA,UAAQ,CAAC,WAAW;QACtB;IACF;IAEA,gCAAgC;IAChC,gBAAyB;QACvB,uCAAmC;;QAAY;QAC/C,MAAM,QAAQ,8HAAA,CAAA,UAAQ,CAAC,WAAW;QAClC,OAAO,CAAC,CAAC;IACX;AACF;AAEA,uCAAuC;AACvC,MAAM,cAAc,IAAI;uCACT", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/services/chatService.ts"], "sourcesContent": ["import httpbase from './httpbase';\nimport { \n  ChatRequest, \n  ChatInConversationRequest, \n  ChatResponse, \n  ChatSession, \n  ChatMessage,\n  Conversation,\n  ConversationCreateRequest \n} from '@/types';\n\nclass ChatService {\n  // Send a chat message (creates new session if none exists)\n  async sendMessage(request: ChatRequest): Promise<ChatResponse> {\n    try {\n      return await httpbase.post<ChatResponse>('/api/chat/', request);\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  // Send message in existing conversation\n  async sendMessageInConversation(request: ChatInConversationRequest): Promise<ChatResponse> {\n    try {\n      return await httpbase.post<ChatResponse>('/api/chat/conversation', request);\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  // Get user's chat sessions\n  async getUserSessions(): Promise<{ sessions: ChatSession[]; total: number }> {\n    try {\n      return await httpbase.get('/api/chat/sessions');\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  // Get conversation history for a session\n  async getSessionHistory(sessionId: string, limit: number = 50): Promise<{\n    session_id: string;\n    messages: ChatMessage[];\n    total_messages: number;\n  }> {\n    try {\n      return await httpbase.get(`/api/chat/history/${sessionId}?limit=${limit}`);\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  // Delete a chat session\n  async deleteSession(sessionId: string): Promise<void> {\n    try {\n      await httpbase.delete(`/api/chat/session/${sessionId}`);\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  // Search messages\n  async searchMessages(query: string, sessionId?: string): Promise<ChatMessage[]> {\n    try {\n      const params = new URLSearchParams({ query });\n      if (sessionId) {\n        params.append('session_id', sessionId);\n      }\n      return await httpbase.get(`/api/chat/search?${params.toString()}`);\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  // Get user conversations\n  async getUserConversations(limit: number = 50, skip: number = 0): Promise<{\n    conversations: Conversation[];\n    total: number;\n  }> {\n    try {\n      return await httpbase.get(`/api/chat/conversations?limit=${limit}&skip=${skip}`);\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  // Create new conversation\n  async createConversation(request: ConversationCreateRequest): Promise<Conversation> {\n    try {\n      return await httpbase.post('/api/chat/conversations', request);\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  // Get conversation details\n  async getConversationDetails(conversationId: string): Promise<Conversation> {\n    try {\n      return await httpbase.get(`/api/chat/conversations/${conversationId}`);\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  // Update conversation\n  async updateConversation(conversationId: string, data: { title?: string; status?: string }): Promise<Conversation> {\n    try {\n      return await httpbase.put(`/api/chat/conversations/${conversationId}`, data);\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  // Delete conversation\n  async deleteConversation(conversationId: string): Promise<void> {\n    try {\n      await httpbase.delete(`/api/chat/conversations/${conversationId}`);\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  // Get user analytics\n  async getUserAnalytics(): Promise<any> {\n    try {\n      return await httpbase.get('/api/chat/analytics/user');\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  // Get session analytics\n  async getSessionAnalytics(sessionId: string): Promise<any> {\n    try {\n      return await httpbase.get(`/api/chat/analytics/session/${sessionId}`);\n    } catch (error) {\n      throw error;\n    }\n  }\n}\n\n// Create and export singleton instance\nconst chatService = new ChatService();\nexport default chatService;\n"], "names": [], "mappings": ";;;AAAA;;AAWA,MAAM;IACJ,2DAA2D;IAC3D,MAAM,YAAY,OAAoB,EAAyB;QAC7D,IAAI;YACF,OAAO,MAAM,8HAAA,CAAA,UAAQ,CAAC,IAAI,CAAe,cAAc;QACzD,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,wCAAwC;IACxC,MAAM,0BAA0B,OAAkC,EAAyB;QACzF,IAAI;YACF,OAAO,MAAM,8HAAA,CAAA,UAAQ,CAAC,IAAI,CAAe,0BAA0B;QACrE,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,2BAA2B;IAC3B,MAAM,kBAAuE;QAC3E,IAAI;YACF,OAAO,MAAM,8HAAA,CAAA,UAAQ,CAAC,GAAG,CAAC;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,kBAAkB,SAAiB,EAAE,QAAgB,EAAE,EAI1D;QACD,IAAI;YACF,OAAO,MAAM,8HAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU,OAAO,EAAE,OAAO;QAC3E,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,wBAAwB;IACxB,MAAM,cAAc,SAAiB,EAAiB;QACpD,IAAI;YACF,MAAM,8HAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,CAAC,kBAAkB,EAAE,WAAW;QACxD,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,kBAAkB;IAClB,MAAM,eAAe,KAAa,EAAE,SAAkB,EAA0B;QAC9E,IAAI;YACF,MAAM,SAAS,IAAI,gBAAgB;gBAAE;YAAM;YAC3C,IAAI,WAAW;gBACb,OAAO,MAAM,CAAC,cAAc;YAC9B;YACA,OAAO,MAAM,8HAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,OAAO,QAAQ,IAAI;QACnE,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,yBAAyB;IACzB,MAAM,qBAAqB,QAAgB,EAAE,EAAE,OAAe,CAAC,EAG5D;QACD,IAAI;YACF,OAAO,MAAM,8HAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,CAAC,8BAA8B,EAAE,MAAM,MAAM,EAAE,MAAM;QACjF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM,mBAAmB,OAAkC,EAAyB;QAClF,IAAI;YACF,OAAO,MAAM,8HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,2BAA2B;QACxD,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,2BAA2B;IAC3B,MAAM,uBAAuB,cAAsB,EAAyB;QAC1E,IAAI;YACF,OAAO,MAAM,8HAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,gBAAgB;QACvE,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAmB,cAAsB,EAAE,IAAyC,EAAyB;QACjH,IAAI;YACF,OAAO,MAAM,8HAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,gBAAgB,EAAE;QACzE,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAmB,cAAsB,EAAiB;QAC9D,IAAI;YACF,MAAM,8HAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,CAAC,wBAAwB,EAAE,gBAAgB;QACnE,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAiC;QACrC,IAAI;YACF,OAAO,MAAM,8HAAA,CAAA,UAAQ,CAAC,GAAG,CAAC;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,wBAAwB;IACxB,MAAM,oBAAoB,SAAiB,EAAgB;QACzD,IAAI;YACF,OAAO,MAAM,8HAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,WAAW;QACtE,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;AACF;AAEA,uCAAuC;AACvC,MAAM,cAAc,IAAI;uCACT", "debugId": null}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/services/index.ts"], "sourcesContent": ["// Barrel export for all services\nexport { default as httpbase } from './httpbase';\nexport { default as authService } from './authService';\nexport { default as chatService } from './chatService';\n"], "names": [], "mappings": "AAAA,iCAAiC;;AACjC;AACA;AACA", "debugId": null}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { authService } from '@/services';\nimport { User, LoginRequest, RegisterRequest } from '@/types';\n\nexport const useAuth = () => {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  useEffect(() => {\n    checkAuth();\n  }, []);\n\n  const checkAuth = async () => {\n    try {\n      setIsLoading(true);\n      \n      if (!authService.hasValidToken()) {\n        setIsAuthenticated(false);\n        setUser(null);\n        return;\n      }\n\n      const isValid = await authService.checkAuth();\n      if (isValid) {\n        const userData = await authService.getCurrentUser();\n        setUser(userData);\n        setIsAuthenticated(true);\n      } else {\n        setIsAuthenticated(false);\n        setUser(null);\n      }\n    } catch (error) {\n      console.error('Auth check failed:', error);\n      setIsAuthenticated(false);\n      setUser(null);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const login = async (credentials: LoginRequest) => {\n    try {\n      const response = await authService.login(credentials);\n      \n      if (response.success && response.user) {\n        setUser(response.user);\n        setIsAuthenticated(true);\n      } else {\n        throw new Error(response.message || 'Login failed');\n      }\n    } catch (error) {\n      throw error;\n    }\n  };\n\n  const register = async (data: RegisterRequest) => {\n    try {\n      const response = await authService.register(data);\n      \n      if (response.success && response.user) {\n        setUser(response.user);\n        setIsAuthenticated(true);\n      } else {\n        throw new Error(response.message || 'Registration failed');\n      }\n    } catch (error) {\n      throw error;\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await authService.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setUser(null);\n      setIsAuthenticated(false);\n    }\n  };\n\n  const updateUser = async (data: Partial<User>) => {\n    try {\n      const updatedUser = await authService.updateCurrentUser(data);\n      setUser(updatedUser);\n      return updatedUser;\n    } catch (error) {\n      throw error;\n    }\n  };\n\n  return {\n    user,\n    isAuthenticated,\n    isLoading,\n    login,\n    register,\n    logout,\n    updateUser,\n    checkAuth,\n  };\n};\n"], "names": [], "mappings": ";;;AAEA;AACA;AAAA;;AAHA;;;AAMO,MAAM,UAAU;;IACrB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR;QACF;4BAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,aAAa;YAEb,IAAI,CAAC,2KAAA,CAAA,cAAW,CAAC,aAAa,IAAI;gBAChC,mBAAmB;gBACnB,QAAQ;gBACR;YACF;YAEA,MAAM,UAAU,MAAM,2KAAA,CAAA,cAAW,CAAC,SAAS;YAC3C,IAAI,SAAS;gBACX,MAAM,WAAW,MAAM,2KAAA,CAAA,cAAW,CAAC,cAAc;gBACjD,QAAQ;gBACR,mBAAmB;YACrB,OAAO;gBACL,mBAAmB;gBACnB,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,mBAAmB;YACnB,QAAQ;QACV,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,QAAQ,OAAO;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,2KAAA,CAAA,cAAW,CAAC,KAAK,CAAC;YAEzC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,QAAQ,SAAS,IAAI;gBACrB,mBAAmB;YACrB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,OAAO,IAAI;YACtC;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,2KAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;YAE5C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,QAAQ,SAAS,IAAI;gBACrB,mBAAmB;YACrB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,OAAO,IAAI;YACtC;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,2KAAA,CAAA,cAAW,CAAC,MAAM;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,QAAQ;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,cAAc,MAAM,2KAAA,CAAA,cAAW,CAAC,iBAAiB,CAAC;YACxD,QAAQ;YACR,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAlGa", "debugId": null}}, {"offset": {"line": 617, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/hooks/index.ts"], "sourcesContent": ["// Barrel export for all hooks\nexport { useTheme } from './useTheme';\nexport { useAuth } from './useAuth';\n"], "names": [], "mappings": "AAAA,8BAA8B;;AAC9B;AACA", "debugId": null}}, {"offset": {"line": 642, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/components/providers/AuthProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext } from 'react';\nimport { AuthContextType } from '@/types';\nimport { useAuth } from '@/hooks';\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuthContext = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuthContext must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: React.ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const auth = useAuth();\n\n  return (\n    <AuthContext.Provider value={auth}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAAA;;;AAJA;;;AAMA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,iBAAiB;;IAC5B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAYN,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;;IACpE,MAAM,OAAO,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAEnB,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IARa;;QACE,0HAAA,CAAA,UAAO;;;KADT", "debugId": null}}, {"offset": {"line": 694, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/components/providers/ThemeProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext } from 'react';\nimport { ThemeContextType } from '@/types';\nimport { useTheme } from '@/hooks';\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport const useThemeContext = () => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useThemeContext must be used within a ThemeProvider');\n  }\n  return context;\n};\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n}\n\nexport const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {\n  const theme = useTheme();\n\n  return (\n    <ThemeContext.Provider value={theme}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAAA;;;AAJA;;;AAMA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,kBAAkB;;IAC7B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAYN,MAAM,gBAA8C,CAAC,EAAE,QAAQ,EAAE;;IACtE,MAAM,QAAQ,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAErB,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;IARa;;QACG,2HAAA,CAAA,WAAQ;;;KADX", "debugId": null}}]}