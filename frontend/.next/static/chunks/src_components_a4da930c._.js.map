{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { ButtonProps } from '@/types';\n\nconst Button: React.FC<ButtonProps> = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  loading = false,\n  onClick,\n  type = 'button',\n  className = '',\n}) => {\n  const baseClasses = 'btn-base relative overflow-hidden';\n  \n  const variantClasses = {\n    primary: 'bg-primary-600 hover:bg-primary-700 text-white shadow-apple focus:ring-primary-500',\n    secondary: 'bg-gray-100 hover:bg-gray-200 dark:bg-dark-200 dark:hover:bg-dark-300 text-gray-900 dark:text-dark-900 focus:ring-gray-500',\n    outline: 'border-2 border-primary-600 text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900/10 focus:ring-primary-500',\n    ghost: 'text-gray-700 dark:text-dark-700 hover:bg-gray-100 dark:hover:bg-dark-200 focus:ring-gray-500',\n  };\n\n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base',\n  };\n\n  const disabledClasses = disabled || loading \n    ? 'opacity-50 cursor-not-allowed' \n    : 'cursor-pointer';\n\n  const combinedClasses = `\n    ${baseClasses}\n    ${variantClasses[variant]}\n    ${sizeClasses[size]}\n    ${disabledClasses}\n    ${className}\n  `.trim();\n\n  return (\n    <motion.button\n      type={type}\n      className={combinedClasses}\n      onClick={onClick}\n      disabled={disabled || loading}\n      whileHover={!disabled && !loading ? { scale: 1.02 } : {}}\n      whileTap={!disabled && !loading ? { scale: 0.98 } : {}}\n      transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n    >\n      {loading && (\n        <motion.div\n          className=\"absolute inset-0 flex items-center justify-center\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.2 }}\n        >\n          <motion.div\n            className=\"w-4 h-4 border-2 border-current border-t-transparent rounded-full\"\n            animate={{ rotate: 360 }}\n            transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n          />\n        </motion.div>\n      )}\n      \n      <motion.span\n        className={loading ? 'opacity-0' : 'opacity-100'}\n        transition={{ duration: 0.2 }}\n      >\n        {children}\n      </motion.span>\n    </motion.button>\n  );\n};\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMA,MAAM,SAAgC,CAAC,EACrC,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,OAAO,EACP,OAAO,QAAQ,EACf,YAAY,EAAE,EACf;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB,YAAY,UAChC,kCACA;IAEJ,MAAM,kBAAkB,CAAC;IACvB,EAAE,YAAY;IACd,EAAE,cAAc,CAAC,QAAQ,CAAC;IAC1B,EAAE,WAAW,CAAC,KAAK,CAAC;IACpB,EAAE,gBAAgB;IAClB,EAAE,UAAU;EACd,CAAC,CAAC,IAAI;IAEN,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,MAAM;QACN,WAAW;QACX,SAAS;QACT,UAAU,YAAY;QACtB,YAAY,CAAC,YAAY,CAAC,UAAU;YAAE,OAAO;QAAK,IAAI,CAAC;QACvD,UAAU,CAAC,YAAY,CAAC,UAAU;YAAE,OAAO;QAAK,IAAI,CAAC;QACrD,YAAY;YAAE,MAAM;YAAU,WAAW;YAAK,SAAS;QAAG;;YAEzD,yBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,QAAQ;oBAAI;oBACvB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAS;;;;;;;;;;;0BAKlE,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,WAAW,UAAU,cAAc;gBACnC,YAAY;oBAAE,UAAU;gBAAI;0BAE3B;;;;;;;;;;;;AAIT;KAtEM;uCAwES", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/components/ui/Input.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { InputProps } from '@/types';\n\nconst Input: React.FC<InputProps> = ({\n  label,\n  placeholder,\n  type = 'text',\n  value,\n  onChange,\n  error,\n  disabled = false,\n  required = false,\n  className = '',\n}) => {\n  const [isFocused, setIsFocused] = useState(false);\n\n  const inputClasses = `\n    input-base\n    ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}\n    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}\n    ${className}\n  `.trim();\n\n  return (\n    <div className=\"space-y-1\">\n      {label && (\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-dark-700\">\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n\n      <div className=\"relative\">\n        <input\n          type={type}\n          value={value}\n          onChange={onChange}\n          placeholder={placeholder}\n          disabled={disabled}\n          required={required}\n          className={inputClasses}\n          onFocus={() => setIsFocused(true)}\n          onBlur={() => setIsFocused(false)}\n        />\n\n        {/* Subtle focus ring animation */}\n        <motion.div\n          className=\"absolute inset-0 rounded-lg border-2 border-primary-400 pointer-events-none\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: isFocused ? 0.2 : 0 }}\n          transition={{ duration: 0.15 }}\n        />\n      </div>\n\n      {error && (\n        <motion.p\n          className=\"text-sm text-red-600 dark:text-red-400\"\n          initial={{ opacity: 0, height: 0 }}\n          animate={{ opacity: 1, height: 'auto' }}\n          exit={{ opacity: 0, height: 0 }}\n          transition={{ duration: 0.2 }}\n        >\n          {error}\n        </motion.p>\n      )}\n    </div>\n  );\n};\n\nexport default Input;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAMA,MAAM,QAA8B,CAAC,EACnC,KAAK,EACL,WAAW,EACX,OAAO,MAAM,EACb,KAAK,EACL,QAAQ,EACR,KAAK,EACL,WAAW,KAAK,EAChB,WAAW,KAAK,EAChB,YAAY,EAAE,EACf;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,CAAC;;IAEpB,EAAE,QAAQ,2DAA2D,GAAG;IACxE,EAAE,WAAW,kCAAkC,GAAG;IAClD,EAAE,UAAU;EACd,CAAC,CAAC,IAAI;IAEN,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAU;;oBACd;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAM;wBACN,OAAO;wBACP,UAAU;wBACV,aAAa;wBACb,UAAU;wBACV,UAAU;wBACV,WAAW;wBACX,SAAS,IAAM,aAAa;wBAC5B,QAAQ,IAAM,aAAa;;;;;;kCAI7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS,YAAY,MAAM;wBAAE;wBACxC,YAAY;4BAAE,UAAU;wBAAK;;;;;;;;;;;;YAIhC,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gBACP,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBACjC,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAO;gBACtC,MAAM;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBAC9B,YAAY;oBAAE,UAAU;gBAAI;0BAE3B;;;;;;;;;;;;AAKX;GAhEM;KAAA;uCAkES", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n  glass?: boolean;\n  padding?: 'none' | 'sm' | 'md' | 'lg';\n}\n\nconst Card: React.FC<CardProps> = ({\n  children,\n  className = '',\n  hover = false,\n  glass = false,\n  padding = 'md',\n}) => {\n  const paddingClasses = {\n    none: '',\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8',\n  };\n\n  const baseClasses = `\n    rounded-xl border transition-smooth\n    ${glass \n      ? 'glass' \n      : 'bg-white dark:bg-dark-100 border-gray-200 dark:border-dark-300 shadow-apple'\n    }\n    ${paddingClasses[padding]}\n    ${className}\n  `.trim();\n\n  return (\n    <motion.div\n      className={baseClasses}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n      whileHover={hover ? { \n        y: -2, \n        boxShadow: '0 8px 32px 0 rgba(0, 0, 0, 0.12)' \n      } : {}}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\nexport default Card;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAaA,MAAM,OAA4B,CAAC,EACjC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,KAAK,EACb,QAAQ,KAAK,EACb,UAAU,IAAI,EACf;IACC,MAAM,iBAAiB;QACrB,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,CAAC;;IAEnB,EAAE,QACE,UACA,8EACH;IACD,EAAE,cAAc,CAAC,QAAQ,CAAC;IAC1B,EAAE,UAAU;EACd,CAAC,CAAC,IAAI;IAEN,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,YAAY,QAAQ;YAClB,GAAG,CAAC;YACJ,WAAW;QACb,IAAI,CAAC;kBAEJ;;;;;;AAGP;KAtCM;uCAwCS", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/components/ui/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Sun, Moon } from 'lucide-react';\nimport { useTheme } from '@/hooks';\n\nconst ThemeToggle: React.FC = () => {\n  const { theme, toggleTheme, isLoading } = useTheme();\n\n  if (isLoading) {\n    return (\n      <div className=\"w-10 h-10 rounded-lg bg-gray-200 dark:bg-dark-200 animate-pulse\" />\n    );\n  }\n\n  return (\n    <motion.button\n      onClick={toggleTheme}\n      className=\"relative w-10 h-10 rounded-lg bg-gray-100 dark:bg-dark-200 hover:bg-gray-200 dark:hover:bg-dark-300 transition-smooth focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\"\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n      transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n    >\n      <motion.div\n        className=\"absolute inset-0 flex items-center justify-center\"\n        initial={false}\n        animate={{\n          rotate: theme === 'dark' ? 180 : 0,\n          scale: theme === 'dark' ? 0 : 1,\n        }}\n        transition={{ duration: 0.3 }}\n      >\n        <Sun className=\"w-5 h-5 text-yellow-500\" />\n      </motion.div>\n      \n      <motion.div\n        className=\"absolute inset-0 flex items-center justify-center\"\n        initial={false}\n        animate={{\n          rotate: theme === 'light' ? -180 : 0,\n          scale: theme === 'light' ? 0 : 1,\n        }}\n        transition={{ duration: 0.3 }}\n      >\n        <Moon className=\"w-5 h-5 text-blue-400\" />\n      </motion.div>\n    </motion.button>\n  );\n};\n\nexport default ThemeToggle;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AAAA;;;AALA;;;;AAOA,MAAM,cAAwB;;IAC5B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAEjD,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;;;;;;IAEnB;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;QACT,WAAU;QACV,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,YAAY;YAAE,MAAM;YAAU,WAAW;YAAK,SAAS;QAAG;;0BAE1D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;gBACT,SAAS;oBACP,QAAQ,UAAU,SAAS,MAAM;oBACjC,OAAO,UAAU,SAAS,IAAI;gBAChC;gBACA,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;;;;;;0BAGjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;gBACT,SAAS;oBACP,QAAQ,UAAU,UAAU,CAAC,MAAM;oBACnC,OAAO,UAAU,UAAU,IAAI;gBACjC;gBACA,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIxB;GA1CM;;QACsC,2HAAA,CAAA,WAAQ;;;KAD9C;uCA4CS", "debugId": null}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\n  size = 'md',\n  className = '',\n}) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12',\n  };\n\n  return (\n    <motion.div\n      className={`${sizeClasses[size]} ${className}`}\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      transition={{ duration: 0.2 }}\n    >\n      <motion.div\n        className=\"w-full h-full border-2 border-primary-200 border-t-primary-600 rounded-full\"\n        animate={{ rotate: 360 }}\n        transition={{\n          duration: 1,\n          repeat: Infinity,\n          ease: \"linear\"\n        }}\n      />\n    </motion.div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAUA,MAAM,iBAAgD,CAAC,EACrD,OAAO,IAAI,EACX,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;QAC9C,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,QAAQ;YAAI;YACvB,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;;;;;;;;;;;AAIR;KA5BM;uCA8BS", "debugId": null}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/components/ui/index.ts"], "sourcesContent": ["// Barrel export for all UI components\nexport { default as Button } from './Button';\nexport { default as Input } from './Input';\nexport { default as Card } from './Card';\nexport { default as ThemeToggle } from './ThemeToggle';\nexport { default as LoadingSpinner } from './LoadingSpinner';\n"], "names": [], "mappings": "AAAA,sCAAsC;;AACtC;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/components/layout/PublicLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { ThemeToggle } from '@/components/ui';\n\ninterface PublicLayoutProps {\n  children: React.ReactNode;\n}\n\nconst PublicLayout: React.FC<PublicLayoutProps> = ({ children }) => {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 dark:from-dark-50 dark:to-dark-100\">\n      {/* Header with theme toggle */}\n      <motion.header\n        className=\"absolute top-0 right-0 p-6 z-10\"\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5 }}\n      >\n        <ThemeToggle />\n      </motion.header>\n\n      {/* Background decoration */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <motion.div\n          className=\"absolute -top-40 -right-40 w-80 h-80 bg-primary-200/30 dark:bg-primary-800/20 rounded-full blur-3xl\"\n          animate={{\n            scale: [1, 1.1, 1],\n            rotate: [0, 180, 360],\n          }}\n          transition={{\n            duration: 20,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n        />\n        <motion.div\n          className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-primary-300/20 dark:bg-primary-700/20 rounded-full blur-3xl\"\n          animate={{\n            scale: [1.1, 1, 1.1],\n            rotate: [360, 180, 0],\n          }}\n          transition={{\n            duration: 25,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n        />\n      </div>\n\n      {/* Main content */}\n      <main className=\"relative z-10\">\n        {children}\n      </main>\n    </div>\n  );\n};\n\nexport default PublicLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAJA;;;;AAUA,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IAC7D,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,6LAAC,oLAAA,CAAA,cAAW;;;;;;;;;;0BAId,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,QAAQ;gCAAC;gCAAG;gCAAK;6BAAI;wBACvB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAK;gCAAG;6BAAI;4BACpB,QAAQ;gCAAC;gCAAK;gCAAK;6BAAE;wBACvB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;;;;;0BAKJ,6LAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;KA/CM;uCAiDS", "debugId": null}}, {"offset": {"line": 645, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/components/providers/index.ts"], "sourcesContent": ["// Barrel export for all providers\nexport { AuthProvider, useAuthContext } from './AuthProvider';\nexport { ThemeProvider, useThemeContext } from './ThemeProvider';\n"], "names": [], "mappings": "AAAA,kCAAkC;;AAClC;AACA", "debugId": null}}, {"offset": {"line": 670, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { \n  MessageSquare, \n  LayoutDashboard, \n  PlayCircle, \n  Settings, \n  LogOut,\n  Menu,\n  X,\n  Plus,\n  Hash\n} from 'lucide-react';\nimport { Button, ThemeToggle } from '@/components/ui';\nimport { useAuthContext } from '@/components/providers';\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },\n  { name: 'Playground', href: '/playground', icon: PlayCircle },\n  { name: 'Settings', href: '/settings', icon: Settings },\n];\n\nconst conversations = [\n  { id: '1', name: 'General Chat', href: '/chat/1' },\n  { id: '2', name: 'Technical Support', href: '/chat/2' },\n  { id: '3', name: 'Project Discussion', href: '/chat/3' },\n];\n\nconst Sidebar: React.FC = () => {\n  const pathname = usePathname();\n  const { user, logout } = useAuthContext();\n  const [isCollapsed, setIsCollapsed] = useState(false);\n\n  const handleLogout = async () => {\n    await logout();\n  };\n\n  return (\n    <>\n      {/* Mobile menu button */}\n      <motion.button\n        className=\"lg:hidden fixed top-4 left-4 z-50 p-2 rounded-lg bg-white dark:bg-dark-100 shadow-apple\"\n        onClick={() => setIsCollapsed(!isCollapsed)}\n        whileHover={{ scale: 1.05 }}\n        whileTap={{ scale: 0.95 }}\n      >\n        {isCollapsed ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n      </motion.button>\n\n      {/* Sidebar */}\n      <AnimatePresence>\n        <motion.div\n          className={`\n            fixed inset-y-0 left-0 z-40 w-64 bg-white dark:bg-dark-100 border-r border-gray-200 dark:border-dark-300 shadow-apple-lg\n            lg:relative lg:translate-x-0\n            ${isCollapsed ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\n          `}\n          initial={{ x: -256 }}\n          animate={{ x: 0 }}\n          exit={{ x: -256 }}\n          transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n        >\n          <div className=\"flex flex-col h-full\">\n            {/* Header */}\n            <motion.div\n              className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-dark-300\"\n              initial={{ opacity: 0, y: -20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.1 }}\n            >\n              <h1 className=\"text-xl font-bold text-gray-900 dark:text-dark-900\">\n                ChatAI\n              </h1>\n              <ThemeToggle />\n            </motion.div>\n\n            {/* Navigation */}\n            <motion.nav\n              className=\"flex-1 px-4 py-6 space-y-2\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 0.2 }}\n            >\n              {navigation.map((item, index) => {\n                const isActive = pathname === item.href;\n                return (\n                  <motion.div\n                    key={item.name}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: 0.1 * index }}\n                  >\n                    <Link\n                      href={item.href}\n                      className={`\n                        flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-smooth\n                        ${isActive\n                          ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'\n                          : 'text-gray-700 dark:text-dark-700 hover:bg-gray-100 dark:hover:bg-dark-200'\n                        }\n                      `}\n                    >\n                      <item.icon className=\"w-5 h-5 mr-3\" />\n                      {item.name}\n                    </Link>\n                  </motion.div>\n                );\n              })}\n            </motion.nav>\n\n            {/* Conversations Section */}\n            <motion.div\n              className=\"px-4 py-4 border-t border-gray-200 dark:border-dark-300\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 0.3 }}\n            >\n              <div className=\"flex items-center justify-between mb-3\">\n                <h3 className=\"text-sm font-medium text-gray-500 dark:text-dark-500 uppercase tracking-wide\">\n                  Conversations\n                </h3>\n                <Button variant=\"ghost\" size=\"sm\" className=\"p-1\">\n                  <Plus className=\"w-4 h-4\" />\n                </Button>\n              </div>\n              \n              <div className=\"space-y-1 max-h-40 overflow-y-auto\">\n                {conversations.map((conversation, index) => (\n                  <motion.div\n                    key={conversation.id}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: 0.1 * index }}\n                  >\n                    <Link\n                      href={conversation.href}\n                      className=\"flex items-center px-3 py-2 rounded-lg text-sm text-gray-600 dark:text-dark-600 hover:bg-gray-100 dark:hover:bg-dark-200 transition-smooth\"\n                    >\n                      <Hash className=\"w-4 h-4 mr-2\" />\n                      <span className=\"truncate\">{conversation.name}</span>\n                    </Link>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n\n            {/* User section */}\n            <motion.div\n              className=\"p-4 border-t border-gray-200 dark:border-dark-300\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.4 }}\n            >\n              <div className=\"flex items-center mb-3\">\n                <div className=\"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white text-sm font-medium\">\n                    {user?.name?.charAt(0).toUpperCase()}\n                  </span>\n                </div>\n                <div className=\"ml-3 flex-1 min-w-0\">\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-dark-900 truncate\">\n                    {user?.name}\n                  </p>\n                  <p className=\"text-xs text-gray-500 dark:text-dark-500 truncate\">\n                    {user?.email}\n                  </p>\n                </div>\n              </div>\n              \n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={handleLogout}\n                className=\"w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20\"\n              >\n                <LogOut className=\"w-4 h-4 mr-2\" />\n                Sign out\n              </Button>\n            </motion.div>\n          </div>\n        </motion.div>\n      </AnimatePresence>\n\n      {/* Overlay for mobile */}\n      {isCollapsed && (\n        <motion.div\n          className=\"lg:hidden fixed inset-0 z-30 bg-black/50\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          onClick={() => setIsCollapsed(false)}\n        />\n      )}\n    </>\n  );\n};\n\nexport default Sidebar;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAAA;AAAA;AACA;AAAA;;;AAlBA;;;;;;;;AAoBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,+NAAA,CAAA,kBAAe;IAAC;IAC/D;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,qNAAA,CAAA,aAAU;IAAC;IAC5D;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,6MAAA,CAAA,WAAQ;IAAC;CACvD;AAED,MAAM,gBAAgB;IACpB;QAAE,IAAI;QAAK,MAAM;QAAgB,MAAM;IAAU;IACjD;QAAE,IAAI;QAAK,MAAM;QAAqB,MAAM;IAAU;IACtD;QAAE,IAAI;QAAK,MAAM;QAAsB,MAAM;IAAU;CACxD;AAED,MAAM,UAAoB;;IACxB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IACtC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe;QACnB,MAAM;IACR;IAEA,qBACE;;0BAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,WAAU;gBACV,SAAS,IAAM,eAAe,CAAC;gBAC/B,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;0BAEvB,4BAAc,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;yCAAe,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;0BAI7D,6LAAC,4LAAA,CAAA,kBAAe;0BACd,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,CAAC;;;YAGV,EAAE,cAAc,kBAAkB,qCAAqC;UACzE,CAAC;oBACD,SAAS;wBAAE,GAAG,CAAC;oBAAI;oBACnB,SAAS;wBAAE,GAAG;oBAAE;oBAChB,MAAM;wBAAE,GAAG,CAAC;oBAAI;oBAChB,YAAY;wBAAE,MAAM;wBAAU,WAAW;wBAAK,SAAS;oBAAG;8BAE1D,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,6LAAC;wCAAG,WAAU;kDAAqD;;;;;;kDAGnE,6LAAC,oLAAA,CAAA,cAAW;;;;;;;;;;;0CAId,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,OAAO;gCAAI;0CAExB,WAAW,GAAG,CAAC,CAAC,MAAM;oCACrB,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,MAAM;wCAAM;kDAEjC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC;;wBAEV,EAAE,WACE,iFACA,4EACH;sBACH,CAAC;;8DAED,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;;;;;;uCAhBP,KAAK,IAAI;;;;;gCAoBpB;;;;;;0CAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA+E;;;;;;0DAG7F,6LAAC,0KAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,WAAU;0DAC1C,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIpB,6LAAC;wCAAI,WAAU;kDACZ,cAAc,GAAG,CAAC,CAAC,cAAc,sBAChC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO,MAAM;gDAAM;0DAEjC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,aAAa,IAAI;oDACvB,WAAU;;sEAEV,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAY,aAAa,IAAI;;;;;;;;;;;;+CAV1C,aAAa,EAAE;;;;;;;;;;;;;;;;0CAkB5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,MAAM,MAAM,OAAO,GAAG;;;;;;;;;;;0DAG3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,MAAM;;;;;;kEAET,6LAAC;wDAAE,WAAU;kEACV,MAAM;;;;;;;;;;;;;;;;;;kDAKb,6LAAC,0KAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS5C,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,SAAS,IAAM,eAAe;;;;;;;;AAKxC;GAvKM;;QACa,qIAAA,CAAA,cAAW;QACH,kJAAA,CAAA,iBAAc;;;KAFnC;uCAyKS", "debugId": null}}, {"offset": {"line": 1141, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { useAuthContext } from '@/components/providers';\nimport { LoadingSpinner } from '@/components/ui';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  redirectTo?: string;\n}\n\nconst ProtectedRoute: React.FC<ProtectedRouteProps> = ({ \n  children, \n  redirectTo = '/auth/login' \n}) => {\n  const { isAuthenticated, isLoading } = useAuthContext();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push(redirectTo);\n    }\n  }, [isAuthenticated, isLoading, router, redirectTo]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-white dark:bg-dark-50\">\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.3 }}\n          className=\"text-center\"\n        >\n          <LoadingSpinner size=\"lg\" className=\"mx-auto mb-4\" />\n          <p className=\"text-gray-600 dark:text-dark-600\">Loading...</p>\n        </motion.div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null; // Will redirect in useEffect\n  }\n\n  return <>{children}</>;\n};\n\nexport default ProtectedRoute;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;;;AANA;;;;;;AAaA,MAAM,iBAAgD,CAAC,EACrD,QAAQ,EACR,aAAa,aAAa,EAC3B;;IACC,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IACpD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBAClC,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAiB;QAAW;QAAQ;KAAW;IAEnD,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAI;gBAClC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,6LAAC,0LAAA,CAAA,iBAAc;wBAAC,MAAK;wBAAK,WAAU;;;;;;kCACpC,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAIxD;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO,MAAM,6BAA6B;IAC5C;IAEA,qBAAO;kBAAG;;AACZ;GAlCM;;QAImC,kJAAA,CAAA,iBAAc;QACtC,qIAAA,CAAA,YAAS;;;KALpB;uCAoCS", "debugId": null}}, {"offset": {"line": 1247, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/components/auth/index.ts"], "sourcesContent": ["// Barrel export for auth components\nexport { default as ProtectedRoute } from './ProtectedRoute';\n"], "names": [], "mappings": "AAAA,oCAAoC;;AACpC", "debugId": null}}, {"offset": {"line": 1279, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/nextai/langraph_test/frontend/src/components/layout/ProtectedLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport Sidebar from './Sidebar';\nimport { ProtectedRoute } from '@/components/auth';\n\ninterface ProtectedLayoutProps {\n  children: React.ReactNode;\n}\n\nconst ProtectedLayout: React.FC<ProtectedLayoutProps> = ({ children }) => {\n  return (\n    <ProtectedRoute>\n      <div className=\"flex h-screen bg-gray-50 dark:bg-dark-50\">\n        {/* Sidebar */}\n        <Sidebar />\n        \n        {/* Main content */}\n        <motion.main\n          className=\"flex-1 overflow-hidden\"\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.3 }}\n        >\n          <div className=\"h-full overflow-y-auto\">\n            {children}\n          </div>\n        </motion.main>\n      </div>\n    </ProtectedRoute>\n  );\n};\n\nexport default ProtectedLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AALA;;;;;AAWA,MAAM,kBAAkD,CAAC,EAAE,QAAQ,EAAE;IACnE,qBACE,6LAAC,4LAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,0IAAA,CAAA,UAAO;;;;;8BAGR,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb;KArBM;uCAuBS", "debugId": null}}]}