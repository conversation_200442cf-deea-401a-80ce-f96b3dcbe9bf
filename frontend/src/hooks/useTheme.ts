'use client';

import { useState, useEffect } from 'react';
import { initializeTheme, applyTheme, setStoredTheme } from '@/utils/theme';

export const useTheme = () => {
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initialTheme = initializeTheme();
    setTheme(initialTheme);
    setIsLoading(false);
  }, []);

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    applyTheme(newTheme);
    setStoredTheme(newTheme);
  };

  return {
    theme,
    toggleTheme,
    isLoading,
  };
};
