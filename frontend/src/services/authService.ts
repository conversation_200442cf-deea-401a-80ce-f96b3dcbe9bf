import httpbase from './httpbase';
import { LoginRequest, RegisterRequest, LoginResponse, User } from '@/types';

class AuthService {
  // Login user
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await httpbase.postFormEncoded('/api/auth/login', {
        username: credentials.username,
        password: credentials.password,
        grant_type: 'password',
      });

      console.log('Login response:', response);

      // Handle OAuth2 response format
      if (response.access_token) {
        httpbase.setToken(response.access_token);

        // Get user info after successful login
        try {
          const user = await this.getCurrentUser();
          return {
            success: true,
            message: 'Login successful',
            user: user,
            access_token: response.access_token,
            token_type: response.token_type || 'bearer'
          };
        } catch (userError) {
          console.warn('Failed to get user info after login:', userError);
          return {
            success: true,
            message: 'Login successful',
            access_token: response.access_token,
            token_type: response.token_type || 'bearer'
          };
        }
      } else {
        throw new Error('No access token received');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      throw new Error(error.message || 'Login failed');
    }
  }

  // Register user
  async register(data: RegisterRequest): Promise<LoginResponse> {
    try {
      const response = await httpbase.post<LoginResponse>('/api/auth/register', data);

      if (response.access_token) {
        httpbase.setToken(response.access_token);
      }

      return response;
    } catch (error) {
      throw error;
    }
  }

  // Get current user info
  async getCurrentUser(): Promise<User> {
    try {
      return await httpbase.get<User>('/api/auth/me');
    } catch (error) {
      throw error;
    }
  }

  // Update current user
  async updateCurrentUser(data: Partial<User>): Promise<User> {
    try {
      return await httpbase.put<User>('/api/auth/me', data);
    } catch (error) {
      throw error;
    }
  }

  // Check if user is authenticated
  async checkAuth(): Promise<boolean> {
    try {
      await httpbase.get('/api/auth/check');
      return true;
    } catch (error) {
      return false;
    }
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      await httpbase.post('/api/auth/logout');
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    } finally {
      httpbase.removeToken();
    }
  }

  // Check if user has valid token
  hasValidToken(): boolean {
    if (typeof window === 'undefined') return false;
    const token = httpbase.getToken();
    return !!token;
  }
}

// Create and export singleton instance
const authService = new AuthService();
export default authService;
