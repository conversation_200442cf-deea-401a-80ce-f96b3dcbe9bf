import httpbase from './httpbase';
import { 
  ChatRequest, 
  ChatInConversationRequest, 
  ChatResponse, 
  ChatSession, 
  ChatMessage,
  Conversation,
  ConversationCreateRequest 
} from '@/types';

class ChatService {
  // Send a chat message (creates new session if none exists)
  async sendMessage(request: ChatRequest): Promise<ChatResponse> {
    try {
      return await httpbase.post<ChatResponse>('/api/chat/', request);
    } catch (error) {
      throw error;
    }
  }

  // Send message in existing conversation
  async sendMessageInConversation(request: ChatInConversationRequest): Promise<ChatResponse> {
    try {
      return await httpbase.post<ChatResponse>('/api/chat/conversation', request);
    } catch (error) {
      throw error;
    }
  }

  // Get user's chat sessions
  async getUserSessions(): Promise<{ sessions: ChatSession[]; total: number }> {
    try {
      return await httpbase.get('/api/chat/sessions');
    } catch (error) {
      throw error;
    }
  }

  // Get conversation history for a session
  async getSessionHistory(sessionId: string, limit: number = 50): Promise<{
    session_id: string;
    messages: ChatMessage[];
    total_messages: number;
  }> {
    try {
      return await httpbase.get(`/api/chat/history/${sessionId}?limit=${limit}`);
    } catch (error) {
      throw error;
    }
  }

  // Delete a chat session
  async deleteSession(sessionId: string): Promise<void> {
    try {
      await httpbase.delete(`/api/chat/session/${sessionId}`);
    } catch (error) {
      throw error;
    }
  }

  // Search messages
  async searchMessages(query: string, sessionId?: string): Promise<ChatMessage[]> {
    try {
      const params = new URLSearchParams({ query });
      if (sessionId) {
        params.append('session_id', sessionId);
      }
      return await httpbase.get(`/api/chat/search?${params.toString()}`);
    } catch (error) {
      throw error;
    }
  }

  // Get user conversations
  async getUserConversations(limit: number = 50, skip: number = 0): Promise<{
    conversations: Conversation[];
    total: number;
  }> {
    try {
      return await httpbase.get(`/api/chat/conversations?limit=${limit}&skip=${skip}`);
    } catch (error) {
      throw error;
    }
  }

  // Create new conversation
  async createConversation(request: ConversationCreateRequest): Promise<Conversation> {
    try {
      return await httpbase.post('/api/chat/conversations', request);
    } catch (error) {
      throw error;
    }
  }

  // Get conversation details
  async getConversationDetails(conversationId: string): Promise<Conversation> {
    try {
      return await httpbase.get(`/api/chat/conversations/${conversationId}`);
    } catch (error) {
      throw error;
    }
  }

  // Update conversation
  async updateConversation(conversationId: string, data: { title?: string; status?: string }): Promise<Conversation> {
    try {
      return await httpbase.put(`/api/chat/conversations/${conversationId}`, data);
    } catch (error) {
      throw error;
    }
  }

  // Delete conversation
  async deleteConversation(conversationId: string): Promise<void> {
    try {
      await httpbase.delete(`/api/chat/conversations/${conversationId}`);
    } catch (error) {
      throw error;
    }
  }

  // Get user analytics
  async getUserAnalytics(): Promise<any> {
    try {
      return await httpbase.get('/api/chat/analytics/user');
    } catch (error) {
      throw error;
    }
  }

  // Get session analytics
  async getSessionAnalytics(sessionId: string): Promise<any> {
    try {
      return await httpbase.get(`/api/chat/analytics/session/${sessionId}`);
    } catch (error) {
      throw error;
    }
  }
}

// Create and export singleton instance
const chatService = new ChatService();
export default chatService;
