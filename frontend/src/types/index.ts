// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

// User Types
export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  is_active: boolean;
  created_at: string;
  last_login?: string;
  preferences: Record<string, any>;
  tenant_info?: TenantInfo;
}

export interface TenantInfo {
  tenant_id: string;
  tenant_name: string;
  tenant_type: string;
  subscription_plan: string;
  max_conversations: number;
  max_messages_per_day: number;
  features: string[];
}

// Auth Types
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  phone: string;
  password: string;
  tenant_info?: TenantInfo;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  user?: User;
  access_token?: string;
  token_type: string;
}

// Chat Types
export interface ChatRequest {
  message: string;
}

export interface ChatInConversationRequest {
  message: string;
  session_id: string;
}

export interface ChatResponse {
  response: string;
  session_id: string;
  conversation_id?: string;
  user_message_id: string;
  assistant_message_id: string;
  response_time_ms: number;
  user_analytics?: Record<string, any>;
  assistant_analytics?: Record<string, any>;
}

export interface ChatSession {
  session_id: string;
  created_at: string;
  updated_at: string;
  message_count: number;
  title?: string;
}

export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: string;
  session_id: string;
  analytics?: Record<string, any>;
}

// Conversation Types
export interface Conversation {
  id: string;
  title?: string;
  created_at: string;
  updated_at: string;
  status: string;
  message_count: number;
  last_message?: string;
}

export interface ConversationCreateRequest {
  title?: string;
  initial_message?: string;
}

// Booking Types
export type ServiceType = 
  | 'general_consultation'
  | 'technical_support'
  | 'course_enrollment'
  | 'loksewa_preparation'
  | 'career_guidance';

export type BookingStatus = 
  | 'pending'
  | 'confirmed'
  | 'cancelled'
  | 'completed'
  | 'no_show';

export interface BookingRequest {
  name: string;
  email: string;
  phone: string;
  service_type: ServiceType;
  date: string;
  time: string;
  notes?: string;
  session_id: string;
}

export interface Booking {
  id: string;
  booking_id: string;
  name: string;
  email: string;
  phone: string;
  service_type: ServiceType;
  date: string;
  time: string;
  notes?: string;
  status: BookingStatus;
  created_at: string;
}

// Dashboard Types
export interface DashboardStats {
  total_users: number;
  total_messages: number;
  total_bookings: number;
  active_sessions: number;
  today_bookings: number;
  today_messages: number;
}

export interface AnalyticsSummary {
  date_range: Record<string, string>;
  message_analytics: Record<string, any>;
  booking_analytics: Record<string, any>;
  user_analytics: Record<string, any>;
}

// UI Types
export interface ThemeContextType {
  theme: 'light' | 'dark';
  toggleTheme: () => void;
}

export interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  register: (data: RegisterRequest) => Promise<void>;
  logout: () => void;
  checkAuth: () => Promise<void>;
}

// Component Props Types
export interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
}

export interface InputProps {
  label?: string;
  placeholder?: string;
  type?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
}

// Error Types
export interface ApiError {
  message: string;
  status: number;
  details?: any;
}

// Navigation Types
export interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  current?: boolean;
  badge?: string | number;
}
