// Theme utility functions
export const getSystemTheme = (): 'light' | 'dark' => {
  if (typeof window === 'undefined') return 'light';
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
};

export const getStoredTheme = (): 'light' | 'dark' | null => {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('theme') as 'light' | 'dark' | null;
};

export const setStoredTheme = (theme: 'light' | 'dark'): void => {
  if (typeof window === 'undefined') return;
  localStorage.setItem('theme', theme);
};

export const applyTheme = (theme: 'light' | 'dark'): void => {
  if (typeof window === 'undefined') return;
  
  const root = window.document.documentElement;
  
  if (theme === 'dark') {
    root.classList.add('dark');
  } else {
    root.classList.remove('dark');
  }
};

export const initializeTheme = (): 'light' | 'dark' => {
  const storedTheme = getStoredTheme();
  const theme = storedTheme || getSystemTheme();
  
  applyTheme(theme);
  setStoredTheme(theme);
  
  return theme;
};
