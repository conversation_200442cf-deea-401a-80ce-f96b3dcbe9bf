'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  MessageSquare, 
  LayoutDashboard, 
  PlayCircle, 
  Settings, 
  LogOut,
  Menu,
  X,
  Plus,
  Hash
} from 'lucide-react';
import { Button, ThemeToggle } from '@/components/ui';
import { useAuthContext } from '@/components/providers';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { name: 'Playground', href: '/playground', icon: PlayCircle },
  { name: 'Settings', href: '/settings', icon: Settings },
];

const conversations = [
  { id: '1', name: 'General Chat', href: '/chat/1' },
  { id: '2', name: 'Technical Support', href: '/chat/2' },
  { id: '3', name: 'Project Discussion', href: '/chat/3' },
];

const Sidebar: React.FC = () => {
  const pathname = usePathname();
  const { user, logout } = useAuthContext();
  const [isCollapsed, setIsCollapsed] = useState(false);

  const handleLogout = async () => {
    await logout();
  };

  return (
    <>
      {/* Mobile menu button */}
      <motion.button
        className="lg:hidden fixed top-4 left-4 z-50 p-2 rounded-lg bg-white dark:bg-dark-100 shadow-apple"
        onClick={() => setIsCollapsed(!isCollapsed)}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        {isCollapsed ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
      </motion.button>

      {/* Sidebar */}
      <AnimatePresence>
        <motion.div
          className={`
            fixed inset-y-0 left-0 z-40 w-64 bg-white dark:bg-dark-100 border-r border-gray-200 dark:border-dark-300 shadow-apple-lg
            lg:relative lg:translate-x-0
            ${isCollapsed ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
          `}
          initial={{ x: -256 }}
          animate={{ x: 0 }}
          exit={{ x: -256 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
        >
          <div className="flex flex-col h-full">
            {/* Header */}
            <motion.div
              className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-dark-300"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <h1 className="text-xl font-bold text-gray-900 dark:text-dark-900">
                ChatAI
              </h1>
              <ThemeToggle />
            </motion.div>

            {/* Navigation */}
            <motion.nav
              className="flex-1 px-4 py-6 space-y-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              {navigation.map((item, index) => {
                const isActive = pathname === item.href;
                return (
                  <motion.div
                    key={item.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 * index }}
                  >
                    <Link
                      href={item.href}
                      className={`
                        flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-smooth
                        ${isActive
                          ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                          : 'text-gray-700 dark:text-dark-700 hover:bg-gray-100 dark:hover:bg-dark-200'
                        }
                      `}
                    >
                      <item.icon className="w-5 h-5 mr-3" />
                      {item.name}
                    </Link>
                  </motion.div>
                );
              })}
            </motion.nav>

            {/* Conversations Section */}
            <motion.div
              className="px-4 py-4 border-t border-gray-200 dark:border-dark-300"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium text-gray-500 dark:text-dark-500 uppercase tracking-wide">
                  Conversations
                </h3>
                <Button variant="ghost" size="sm" className="p-1">
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
              
              <div className="space-y-1 max-h-40 overflow-y-auto">
                {conversations.map((conversation, index) => (
                  <motion.div
                    key={conversation.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 * index }}
                  >
                    <Link
                      href={conversation.href}
                      className="flex items-center px-3 py-2 rounded-lg text-sm text-gray-600 dark:text-dark-600 hover:bg-gray-100 dark:hover:bg-dark-200 transition-smooth"
                    >
                      <Hash className="w-4 h-4 mr-2" />
                      <span className="truncate">{conversation.name}</span>
                    </Link>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* User section */}
            <motion.div
              className="p-4 border-t border-gray-200 dark:border-dark-300"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {user?.name?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="ml-3 flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-dark-900 truncate">
                    {user?.name}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-dark-500 truncate">
                    {user?.email}
                  </p>
                </div>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleLogout}
                className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign out
              </Button>
            </motion.div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Overlay for mobile */}
      {isCollapsed && (
        <motion.div
          className="lg:hidden fixed inset-0 z-30 bg-black/50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={() => setIsCollapsed(false)}
        />
      )}
    </>
  );
};

export default Sidebar;
