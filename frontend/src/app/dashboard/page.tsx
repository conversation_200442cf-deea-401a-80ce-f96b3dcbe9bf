'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  MessageSquare, 
  Users, 
  Calendar, 
  TrendingUp,
  Activity,
  Clock
} from 'lucide-react';
import { Card, LoadingSpinner } from '@/components/ui';
import { ProtectedLayout } from '@/components/layout';
import { useAuthContext } from '@/components/providers';

interface DashboardStats {
  total_users: number;
  total_messages: number;
  total_bookings: number;
  active_sessions: number;
  today_bookings: number;
  today_messages: number;
}

const DashboardPage: React.FC = () => {
  const { user } = useAuthContext();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate API call for dashboard stats
    const fetchStats = async () => {
      try {
        // This would be replaced with actual API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setStats({
          total_users: 1250,
          total_messages: 15420,
          total_bookings: 342,
          active_sessions: 28,
          today_bookings: 12,
          today_messages: 156,
        });
      } catch (error) {
        console.error('Failed to fetch stats:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, []);

  const statCards = [
    {
      title: 'Total Messages',
      value: stats?.total_messages || 0,
      icon: MessageSquare,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20',
      change: '+12%',
    },
    {
      title: 'Active Users',
      value: stats?.total_users || 0,
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/20',
      change: '+8%',
    },
    {
      title: 'Total Bookings',
      value: stats?.total_bookings || 0,
      icon: Calendar,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/20',
      change: '+15%',
    },
    {
      title: 'Active Sessions',
      value: stats?.active_sessions || 0,
      icon: Activity,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100 dark:bg-orange-900/20',
      change: '+5%',
    },
  ];

  const recentActivity = [
    { id: 1, action: 'New user registered', time: '2 minutes ago', type: 'user' },
    { id: 2, action: 'Chat session started', time: '5 minutes ago', type: 'chat' },
    { id: 3, action: 'Booking confirmed', time: '10 minutes ago', type: 'booking' },
    { id: 4, action: 'Message sent', time: '15 minutes ago', type: 'message' },
  ];

  return (
    <ProtectedLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-3xl font-bold text-gray-900 dark:text-dark-900">
            Welcome back, {user?.name}!
          </h1>
          <p className="text-gray-600 dark:text-dark-600 mt-2">
            Here's what's happening with your account today.
          </p>
        </motion.div>

        {/* Stats Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="h-32">
                <div className="flex items-center justify-center h-full">
                  <LoadingSpinner />
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {statCards.map((stat, index) => (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
              >
                <Card hover className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-dark-600">
                        {stat.title}
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-dark-900 mt-2">
                        {stat.value.toLocaleString()}
                      </p>
                      <p className="text-sm text-green-600 mt-1">
                        {stat.change} from last month
                      </p>
                    </div>
                    <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                      <stat.icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-dark-900">
                  Recent Activity
                </h2>
                <Clock className="w-5 h-5 text-gray-400" />
              </div>
              
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <motion.div
                    key={activity.id}
                    className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-dark-200 transition-smooth"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 * index }}
                  >
                    <div className="w-2 h-2 bg-primary-600 rounded-full" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900 dark:text-dark-900">
                        {activity.action}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-dark-500">
                        {activity.time}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <Card>
            <div className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-dark-900 mb-6">
                Quick Actions
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <motion.button
                  className="p-4 text-left rounded-lg border border-gray-200 dark:border-dark-300 hover:border-primary-300 dark:hover:border-primary-600 transition-smooth"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <MessageSquare className="w-8 h-8 text-primary-600 mb-2" />
                  <h3 className="font-medium text-gray-900 dark:text-dark-900">
                    Start New Chat
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-dark-600">
                    Begin a new conversation
                  </p>
                </motion.button>
                
                <motion.button
                  className="p-4 text-left rounded-lg border border-gray-200 dark:border-dark-300 hover:border-primary-300 dark:hover:border-primary-600 transition-smooth"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Calendar className="w-8 h-8 text-primary-600 mb-2" />
                  <h3 className="font-medium text-gray-900 dark:text-dark-900">
                    Schedule Meeting
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-dark-600">
                    Book a consultation
                  </p>
                </motion.button>
                
                <motion.button
                  className="p-4 text-left rounded-lg border border-gray-200 dark:border-dark-300 hover:border-primary-300 dark:hover:border-primary-600 transition-smooth"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <TrendingUp className="w-8 h-8 text-primary-600 mb-2" />
                  <h3 className="font-medium text-gray-900 dark:text-dark-900">
                    View Analytics
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-dark-600">
                    Check your statistics
                  </p>
                </motion.button>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </ProtectedLayout>
  );
};

export default DashboardPage;
